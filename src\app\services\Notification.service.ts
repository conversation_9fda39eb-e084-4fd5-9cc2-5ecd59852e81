import { isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { getMessaging, getToken, isSupported, Messaging, onMessage } from 'firebase/messaging';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { initializeApp } from 'firebase/app';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  private messaging: Messaging | null = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  public currentToken$ = new BehaviorSubject<string | null>(null);
  public message$ = new BehaviorSubject<any>(null);
  public isSupported$ = new BehaviorSubject<boolean>(false);
  public permissionStatus$ = new BehaviorSubject<NotificationPermission>('default');

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      this.initializeFirebase();
    }
  }

  private async initializeFirebase(): Promise<void> {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  private async performInitialization(): Promise<void> {
    try {
      console.log('Initializing Firebase Messaging...');

      // Check if Firebase Messaging is supported
      const supported = await isSupported();
      this.isSupported$.next(supported);

      if (!supported) {
        console.warn('Firebase Messaging is not supported in this environment.');
        return;
      }

      // Initialize Firebase app
      initializeApp(environment.firebaseConfig);

      // Get messaging instance
      this.messaging = getMessaging();
      this.isInitialized = true;

      // Check current permission status
      if ('Notification' in window) {
        this.permissionStatus$.next(Notification.permission);
      }

      console.log('Firebase Messaging initialized successfully');
    } catch (error) {
      console.error('Error initializing Firebase Messaging:', error);
      throw error;
    }
  }

  async requestPermission(): Promise<string | null> {
    if (!isPlatformBrowser(this.platformId)) {
      console.warn('Not running in browser environment');
      return null;
    }

    try {
      // Ensure Firebase is initialized
      await this.initializeFirebase();

      if (!this.messaging) {
        console.error('Firebase Messaging not initialized');
        return null;
      }

      // Check if notifications are supported
      if (!('Notification' in window)) {
        console.error('This browser does not support notifications');
        return null;
      }

      // Request notification permission
      console.log('Requesting notification permission...');
      const permission = await Notification.requestPermission();
      this.permissionStatus$.next(permission);

      if (permission !== 'granted') {
        console.warn('Notification permission not granted:', permission);
        return null;
      }

      console.log('Notification permission granted, getting FCM token...');

      // Get FCM token
      const token = await getToken(this.messaging, {
        vapidKey: 'BH8-SVxnCB9RieA62u4fzlU2HGZhcnUSKd-0PjrEvXXvW5-ERtcpMtI9PlYOx0YA-HBmzbvlaCR2GtGqrqWm28Y',
      });

      if (token) {
        console.log('FCM Token received:', token);
        this.currentToken$.next(token);
        return token;
      } else {
        console.warn('No FCM registration token available');
        return null;
      }
    } catch (error) {
      console.error('Error requesting FCM permission and token:', error);
      return null;
    }
  }

  async listen(): Promise<void> {
    if (!isPlatformBrowser(this.platformId)) {
      console.warn('Not running in browser environment');
      return;
    }

    try {
      // Ensure Firebase is initialized
      await this.initializeFirebase();

      if (!this.messaging) {
        console.error('Firebase Messaging not initialized');
        return;
      }

      console.log('Setting up FCM message listener...');

      onMessage(this.messaging, (payload) => {
        console.log('FCM Message received in foreground:', payload);
        this.message$.next(payload);

        // Show notification if permission is granted
        if (Notification.permission === 'granted') {
          this.showNotification(payload);
        }
      });

      console.log('FCM message listener set up successfully');
    } catch (error) {
      console.error('Error setting up FCM message listener:', error);
    }
  }

  private showNotification(payload: any): void {
    const title = payload.notification?.title || 'Pro Care Notification';
    const options = {
      body: payload.notification?.body || 'You have a new notification',
      icon: '/assets/images/icons/favicon-32x32.png',
      badge: '/assets/images/icons/favicon-16x16.png',
      tag: 'procare-notification',
      requireInteraction: true
    };

    new Notification(title, options);
  }

  // Helper methods for checking status
  isMessagingSupported(): Observable<boolean> {
    return this.isSupported$.asObservable();
  }

  getPermissionStatus(): Observable<NotificationPermission> {
    return this.permissionStatus$.asObservable();
  }

  getCurrentToken(): Observable<string | null> {
    return this.currentToken$.asObservable();
  }

  getMessages(): Observable<any> {
    return this.message$.asObservable();
  }
}
