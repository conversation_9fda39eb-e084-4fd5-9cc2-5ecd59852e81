export const environment = {
  production: false,
  apiUrl: 'http://procare.runasp.net',
  defaultLanguage: 'en',
  supportedLanguages: ['en', 'ar'],
  appName: 'CoreUI Admin',
  version: '1.0.0',
  // Add other environment-specific variables here
  firebase: {
    apiKey: '',
    authDomain: '',
    projectId: '',
    storageBucket: '',
    messagingSenderId: '',
    appId: ''
  },
  // Add any other configuration variables your app needs
  features: {
    enableNotifications: true,
    enableAnalytics: true,
    enableChat: false
  },
  firebaseConfig: {
        apiKey: "AIzaSyDqu_FG2UAVffwZsErtaA7mS6ylDgU8xEs",
        authDomain: "procare-85759.firebaseapp.com",
        projectId: "procare-85759",
        storageBucket: "procare-85759.firebasestorage.app",
        messagingSenderId: "680646923711",
        appId: "1:680646923711:web:f8e0449399dc95249a7d0a",
        measurementId: "G-L93HNVVFVD"
    }
};
